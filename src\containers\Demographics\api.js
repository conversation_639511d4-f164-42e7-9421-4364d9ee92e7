import { useMutation } from '@tanstack/react-query';
import { getMachineAccessToken } from '@/network/authAPIUtility';
import { ORGANIZATION, ORGANIZATION_ID, REQUEST_ID } from '@/utils/constants';
import { GET_ORGANIZATION_REQUEST, SEND_OTP, VERIFY_OTP } from '@/utils/constants/awsApiEndpoints';
import { getOrganizationAndWidgetId, getParamFromUrl } from '@/containers/commonUtility';
import { makeFetchNetworkCall } from '@/network';

export const useSendOtp = () => {
  return useMutation({
    mutationKey: ['sendOtp'],
    mutationFn: async (contactInfo) => {
      const payload =
        contactInfo.type === 'Email' ? { emailAddress: contactInfo.value } : { phoneNumber: contactInfo.value };
      return await makeOtpCall(SEND_OTP, payload);
    },
  });
};

export const useVerifyOtp = () => {
  return useMutation({
    mutationKey: ['verifyOtp'],
    mutationFn: async ({ type, value, otp }) => {
      const payload = type === 'Email' ? { emailAddress: value, otp } : { phoneNumber: value, otp };
      return await makeOtpCall(VERIFY_OTP, payload);
    },
  });
};

const makeOtpCall = async (url, payload) => {
  try {
    const machineAccessToken = await getMachineAccessToken(ORGANIZATION);
    const [organizationId] = getOrganizationAndWidgetId();
    const URL = url.replace(ORGANIZATION_ID, organizationId);

    const response = await fetch(URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${machineAccessToken}`,
      },
      body: JSON.stringify(payload),
    });

    if (response.ok && response.headers.get('content-length') === '0') {
      return { success: true };
    }

    if (response.ok) {
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        return data;
      }
      return { success: true };
    }

    const errorData = await response.json().catch(() => ({ message: 'Unknown error occurred' }));
    throw new Error(errorData.message || 'Some error occurred');
  } catch (error) {
    throw error;
  }
};

export const validateRequestId = async (requestId, orgId) => {
  try {
    if (!requestId) {
      return { status: 404 };
    }

    const url = GET_ORGANIZATION_REQUEST.replace(ORGANIZATION_ID, orgId).replace(REQUEST_ID, requestId);
    const response = await makeFetchNetworkCall({ method: 'GET', url: url });
    return response;
  } catch (error) {
    console.error('Error validating request ID:', error);
    return error;
  }
};
