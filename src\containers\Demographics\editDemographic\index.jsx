import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { ConsentAgreement, HeadingAndDescription, Loader } from '@/components';
import { Button, Typography, Box, Grid } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { Individual } from './individual';
import { Contact } from './contact';
import { AllDemographic } from './allDemographic';
import {
  validateAllFields,
  addIndividualArrayField,
  removeIndividualArrayField,
  contactMethodValidation,
} from '@/utils/helpers/validation';
import { isValidationDataValid } from '@/utils/dataValidation/dataValidation';
import { actions, FOUND, GET_ORG_REQUEST_QUERY_KEY, NOT_FOUND, requestStatuses } from '../../commonConstants';
import { useTranslation } from 'react-i18next';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { resetCreateConnectionAtConnectionIndex } from '@/redux/actions/createConnectionAtConnectionIndex';
import useNotification from '@/hooks/useNotification';
import { getSignedIndividualUserDemographic } from '@/containers/Demographics/demographicUtility';
import { validateRequestId } from '../api';
import { useQueryClient } from '@tanstack/react-query';
import { endOfDay, isAfter, isBefore, isEqual, startOfDay } from 'date-fns';
import { getOrganizationAndWidgetId, getParamFromUrl } from '@/containers/commonUtility';

const initialDemographicState = {
  id: null,
  resourceType: null,
  preferredContactMethod: '',
  subscribeToNotifications: true,
  individuals: [
    {
      firstName: '',
      middleName: '',
      lastName: '',
      gender: '',
      dateOfBirth: '',
      healthCareIds: [],
    },
  ],
  contact: {
    emailAddresses: [],
    phoneNumbers: [],
    addresses: [],
  },
};

const isDateWithinRange = (requestedDate, /* dueDate, */ expiryDate) => {
  const reqDate = new Date(requestedDate);
  // const due = startOfDay(new Date(dueDate));
  const expiry = endOfDay(new Date(expiryDate));

  // console.log({ reqDate, due, expiry });

  // const isAfterOrEqualDue = isAfter(reqDate, due) || isEqual(reqDate, due);
  const isBeforeOrEqualExpiry = isBefore(reqDate, expiry) || isEqual(reqDate, expiry);

  console.log({ /* isAfterOrEqualDue,  */ isBeforeOrEqualExpiry });

  // return isAfterOrEqualDue && isBeforeOrEqualExpiry;
  return isBeforeOrEqualExpiry;
};

export const EditDemographic = (props) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [, sendNotification] = useNotification();
  const queryClient = useQueryClient();

  const {
    title,
    subtitle,
    handleDemographicCreationCallback,
    fields,
    widgetType,
    demographic,
    isClientSummary,
    isMultipleIndividualEnabled,
    isConsentRequired,
    handleSummaryNavigationCallback,
    provinces,
    selfRegistration,
    clientNotFoundPage,
    otpVerificationEnabled,
    byRequestOnly,
    requestNotFoundPage,
  } = props;

  const clientNotFoundPageDetails = {
    heading: clientNotFoundPage?.heading || t('individualNotFoundHeading'),
    description: clientNotFoundPage?.description || t('individualNotFoundDescription'),
  };

  const requestNotFoundPageDetails = {
    heading: requestNotFoundPage?.heading || t('requestNotFound'),
    description: requestNotFoundPage?.description || t('Please get in touch with us'),
  };

  const {
    fetchSearchIndividualAtClientIndex,
    makeConnectionAtConnectionIndex,
    getClientId,
    checkConnectionAtConnectionIndex,
  } = useCommonAPIs();

  const {
    isSearchIndividualAtClientIndexFetching,
    searchIndividualAtClientIndexSuccessData,
    searchIndividualAtClientIndexError,
    searchIndividualAtClientIndexErrorData,
  } = useSelector((state) => state.searchIndividualAtClientIndexReducer);

  const {
    isCreateConnectionAtConnectionIndexFetching,
    createConnectionAtConnectionIndexSuccess,
    createConnectionAtConnectionIndexErrorData,
  } = useSelector((state) => state.createConnectionAtConnectionIndexReducer);
  const {
    checkExistingConnectionIndexSuccess,
    checkExistingConnectionIndexError,
    isCheckExistingConnectionIndexFetching,
  } = useSelector((state) => state.checkExistingConnectionIndexReducer) || {};

  const { individualUserInfoSuccessData } = useSelector((state) => state.individualUserInfoReducer);

  const { getIndividualDataSuccess, getIndividualDataError, getIndividualDataErrorData, getIndividualDataSuccessData } =
    useSelector((state) => state.getIndividualDataReducer);

  // Form States
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [isConsented, setIsConsented] = useState(false);
  const [validationData, setValidationData] = useState({});
  const [demographicFields, setDemographicFields] = useState(
    demographic ? demographic : structuredClone(initialDemographicState),
  );
  const [isClientSearched, setIsClientSearched] = useState(false);
  const [isClientFound, setIsClientFound] = useState(null);
  const [primaryEmailVerified, setPrimaryEmailVerified] = useState(false);
  const [primaryPhoneVerified, setPrimaryPhoneVerified] = useState(false);
  const [verifiedValues, setVerifiedValues] = useState(new Set());
  const [requestStatus, setRequestStatus] = useState(requestStatuses.VALID);
  const [requestVerificationPending, setRequestVerificationPending] = useState(false);

  useEffect(() => {
    if (getIndividualDataSuccess) {
      setDemographicFields(getSignedIndividualUserDemographic(demographic, getIndividualDataSuccessData, fields));
      setShowValidationErrors(false);
    }
    if (getIndividualDataError) {
      console.log('getIndividualDataErrorData: ', getIndividualDataErrorData);
    }
  }, [getIndividualDataSuccess, getIndividualDataError, getIndividualDataErrorData, getIndividualDataSuccessData]);

  useEffect(() => {
    if (demographic) {
      setDemographicFields(demographic);
    }
  }, [demographic]);

  useEffect(() => {
    const primaryEmail = demographicFields.contact.emailAddresses.find((email) => email.primary)?.emailAddress;
    const primaryPhone = demographicFields.contact.phoneNumbers.find((phone) => phone.primary)?.phoneNumber;

    setPrimaryEmailVerified(primaryEmail ? verifiedValues.has(primaryEmail) : false);
    setPrimaryPhoneVerified(primaryPhone ? verifiedValues.has(primaryPhone) : false);
  }, [verifiedValues, demographicFields.contact.emailAddresses, demographicFields.contact.phoneNumbers]);

  const handleAddMultipleIndividual = () => {
    setDemographicFields({
      ...demographicFields,
      individuals: [
        ...demographicFields.individuals,
        {
          firstName: '',
          middleName: '',
          lastName: '',
          gender: '',
          dateOfBirth: '',
          healthCareIds: [],
        },
      ],
    });
    addIndividualArrayField();
  };

  const handleRemoveIndividual = (index) => {
    if (demographicFields?.individuals.length > 1) {
      const tempArray = [...demographicFields.individuals];
      tempArray.splice(index, 1);
      setDemographicFields({
        ...demographicFields,
        individuals: tempArray,
      });
      removeIndividualArrayField(index);
    }
  };

  const handleIndividualChangeCallback = (value, index, individualFieldValidation) => {
    setDemographicFields({
      ...demographicFields,
      individuals: demographicFields.individuals.map((individual, i) =>
        i === index ? { ...individual, ...value } : individual,
      ),
    });

    setValidationData(individualFieldValidation);
  };

  const handleContactChangeCallback = (value, contactFieldValidationData) => {
    setValidationData(contactFieldValidationData);
  };

  const handlePreferredContactMethod = (value) => {
    let normalizedValue = value;
    if (value) {
      if (value.toLowerCase() === 'email' || value.toLowerCase() === t('email').toLowerCase()) {
        normalizedValue = 'Email';
      } else if (value.toLowerCase() === 'phone' || value.toLowerCase() === t('phone').toLowerCase()) {
        normalizedValue = 'Phone';
      }
    }

    setDemographicFields({
      ...demographicFields,
      preferredContactMethod: normalizedValue,
    });
    setValidationData(contactMethodValidation(fields, normalizedValue, demographicFields));
  };

  const handleRequestValidation = async (clientId) => {
    const requestId = getParamFromUrl('requestId');
    const [organizationId] = getOrganizationAndWidgetId();

    const requestResponse = await queryClient.fetchQuery({
      queryKey: GET_ORG_REQUEST_QUERY_KEY,
      queryFn: () => validateRequestId(requestId, organizationId),
    });
    setRequestVerificationPending(false);

    if (requestResponse?.status === 200) {
      const {
        clientId: apiClientId,
        requestId: apiRequestId,
        organizationId: apiOrganizationId,
        requestedDate,
        dueDate,
        expiryDate,
      } = requestResponse?.data || {};
      // Validate API response
      const isClientIdMatch = apiClientId === clientId;
      const isRequestIdMatch = requestId === apiRequestId;
      const isOrganizationIdMatch = organizationId === apiOrganizationId;
      // TODO: Ask Belle if we need to validate date range, if yes, what is the difference between due and expiry date
      // const isDateInRange = isDateWithinRange(requestedDate, dueDate, expiryDate);
      const isBeforeOrEqualExpiry = isBefore(requestedDate, expiryDate) || isEqual(requestedDate, expiryDate);
      if (!isClientIdMatch || !isRequestIdMatch || !isOrganizationIdMatch || !isBeforeOrEqualExpiry) {
        setRequestStatus(requestStatuses.INVALID);
        return;
      }
    } else {
      setRequestStatus(requestStatuses.INVALID);
      return;
    }
  };

  // this flag is to trigger the above useEffect for setting up validation Data
  const [triggerUseEffect, setTriggerUseEffect] = useState(null);

  useEffect(() => {
    if (
      individualUserInfoSuccessData &&
      !searchIndividualAtClientIndexSuccessData &&
      !checkExistingConnectionIndexSuccess &&
      !isSearchIndividualAtClientIndexFetching &&
      !checkExistingConnectionIndexError &&
      !isCheckExistingConnectionIndexFetching
    ) {
      checkConnectionAtConnectionIndex();
    }
  }, [
    individualUserInfoSuccessData,
    searchIndividualAtClientIndexSuccessData,
    checkExistingConnectionIndexSuccess,
    isSearchIndividualAtClientIndexFetching,
    checkExistingConnectionIndexError,
    isCheckExistingConnectionIndexFetching,
  ]);

  useEffect(() => {
    (async () => {
      if (isClientSearched && searchIndividualAtClientIndexSuccessData) {
        const { found, identifiers, clientId } = searchIndividualAtClientIndexSuccessData?.data || {};
        setIsClientFound(found ? FOUND : NOT_FOUND);

        if (!found) return;

        if (byRequestOnly && requestVerificationPending) {
          await handleRequestValidation(clientId);
          setRequestVerificationPending(false);
        }

        if (individualUserInfoSuccessData && checkExistingConnectionIndexError) {
          makeConnectionAtConnectionIndex(clientId);
        } else {
          if (!requestVerificationPending && requestStatus !== requestStatuses.INVALID) {
            handleDemographicCreationCallback(actions.NEXT, {
              ...demographicFields,
              clientIdentifiers: identifiers,
            });
            setIsClientSearched(false);
          }
        }
      }
    })();
  }, [
    searchIndividualAtClientIndexSuccessData,
    individualUserInfoSuccessData,
    checkExistingConnectionIndexError,
    requestVerificationPending,
  ]);

  useEffect(() => {
    if (searchIndividualAtClientIndexError && searchIndividualAtClientIndexErrorData) {
      setIsClientSearched(false);
      if (
        searchIndividualAtClientIndexErrorData.statusCode === 400 ||
        searchIndividualAtClientIndexErrorData.statusCode === 500
      ) {
        let errorMessage = t('apiError');

        if (
          searchIndividualAtClientIndexErrorData.errorDetails &&
          searchIndividualAtClientIndexErrorData.errorDetails.length > 0
        ) {
          const firstError = searchIndividualAtClientIndexErrorData.errorDetails[0];
          if (firstError.errorDetails && firstError.errorDetails.length > 0) {
            errorMessage = firstError.errorDetails[0].message || firstError.message || errorMessage;
          } else {
            errorMessage = firstError.message || errorMessage;
          }
        }

        sendNotification({ variant: 'error', msg: errorMessage });
      } else {
        sendNotification({ variant: 'error', msg: t('apiError') });
      }
    }
  }, [searchIndividualAtClientIndexError, searchIndividualAtClientIndexErrorData]);

  useEffect(() => {
    if (requestVerificationPending) return;

    if (createConnectionAtConnectionIndexSuccess) {
      setIsClientSearched(false);
      dispatch(resetCreateConnectionAtConnectionIndex());

      handleDemographicCreationCallback(actions.NEXT, demographicFields);
    }
    if (createConnectionAtConnectionIndexErrorData?.message?.includes('Active connection')) {
      setIsClientSearched(false);

      dispatch(resetCreateConnectionAtConnectionIndex());
      handleDemographicCreationCallback(actions.NEXT, demographicFields);
    }
  }, [
    createConnectionAtConnectionIndexSuccess,
    createConnectionAtConnectionIndexErrorData,
    requestVerificationPending,
  ]);

  useEffect(() => {
    if (demographic && showValidationErrors) {
      setValidationData(validateAllFields(fields, demographicFields));
    }
  }, [demographic, demographicFields, triggerUseEffect, showValidationErrors]);

  const handleTryAgain = () => {
    setIsClientFound(null);
    setIsConsented(false);
  };

  const autoFillDemographic = () => {
    setDemographicFields({
      id: null,
      resourceType: null,
      preferredContactMethod: '',
      subscribeToNotifications: true,
      individuals: [
        {
          firstName: 'Donald',
          middleName: '',
          lastName: 'Draper',
          gender: '',
          dateOfBirth: '1998-04-01',
          healthCareIds: [
            {
              type: 'PHN',
              issuer: 'BC',
              value: '**********',
              primary: true,
            },
          ],
        },
      ],
      contact: {
        emailAddresses: [
          {
            emailAddress: '<EMAIL>',
            primary: true,
          },
        ],
        phoneNumbers: [],
        addresses: [],
      },
    });
  };

  const handleVerificationSuccess = (type, isVerified = true, value) => {
    if (value && isVerified) {
      setVerifiedValues((prev) => new Set([...prev, value]));

      const primaryEmail = demographicFields.contact.emailAddresses.find((email) => email.primary)?.emailAddress;
      const primaryPhone = demographicFields.contact.phoneNumbers.find((phone) => phone.primary)?.phoneNumber;

      if (type === 'email' && primaryEmail === value) {
        setPrimaryEmailVerified(true);
      } else if (type === 'phone' && primaryPhone === value) {
        setPrimaryPhoneVerified(true);
      }
    } else if (value && !isVerified) {
      setVerifiedValues((prev) => {
        const newSet = new Set([...prev]);
        newSet.delete(value);
        return newSet;
      });

      const primaryEmail = demographicFields.contact.emailAddresses.find((email) => email.primary)?.emailAddress;
      const primaryPhone = demographicFields.contact.phoneNumbers.find((phone) => phone.primary)?.phoneNumber;

      if (type === 'email' && primaryEmail === value) {
        setPrimaryEmailVerified(false);
      } else if (type === 'phone' && primaryPhone === value) {
        setPrimaryPhoneVerified(false);
      }
    }
  };

  const needsVerification = () => {
    if (!otpVerificationEnabled) return false;

    const emailField = fields.find((field) => field.code === 'EMAIL');
    const phoneField = fields.find((field) => field.code === 'PHONE');

    const isEmailMandatory = emailField?.isMandatory === true;
    const isPhoneMandatory = phoneField?.isMandatory === true;

    const primaryEmail = demographicFields.contact.emailAddresses.find((email) => email.primary);
    const primaryPhone = demographicFields.contact.phoneNumbers.find((phone) => phone.primary);

    const preferredMethod = demographicFields.preferredContactMethod;
    const isEmailPreferred = preferredMethod === 'Email';
    const isPhonePreferred = preferredMethod === 'Phone';

    const needsEmailVerification = (isEmailMandatory || isEmailPreferred) && primaryEmail?.emailAddress;
    const needsPhoneVerification = (isPhoneMandatory || isPhonePreferred) && primaryPhone?.phoneNumber;

    const isPrimaryEmailUnverified = needsEmailVerification && !primaryEmailVerified;
    const isPrimaryPhoneUnverified = needsPhoneVerification && !primaryPhoneVerified;

    return isPrimaryEmailUnverified || isPrimaryPhoneUnverified;
  };

  const hasValidationErrors = () => {
    if (!showValidationErrors) return false;
    return !isValidationDataValid(validationData);
  };

  const handleOnClick = async () => {
    setShowValidationErrors(true);
    const validatedData = validateAllFields(fields, demographicFields);
    setValidationData(validatedData);
    const isDataValid = isValidationDataValid(validatedData);

    if (isDataValid) {
      if (byRequestOnly) setRequestVerificationPending(true);
      fetchSearchIndividualAtClientIndex(demographicFields, selfRegistration);
      setIsClientSearched(true);
    } else {
      setTriggerUseEffect(!triggerUseEffect);
    }
  };

  if (isClientFound === NOT_FOUND) {
    return (
      <HeadingAndDescription
        headingAndDescriptionData={clientNotFoundPageDetails}
        handleNavigationCallback={handleTryAgain}
        showNextButton={false}
      />
    );
  }

  if (requestStatus === requestStatuses.INVALID) {
    return <HeadingAndDescription headingAndDescriptionData={requestNotFoundPageDetails} showNextButton={false} />;
  }

  return (
    <>
      <Loader
        active={
          isSearchIndividualAtClientIndexFetching ||
          isCreateConnectionAtConnectionIndexFetching ||
          requestVerificationPending
        }
      />
      <Box>
        {process.env.NODE_ENV !== 'production' && (
          <>
            <button onClick={autoFillDemographic}>Autofill demographics</button>
          </>
        )}
        <Grid container alignItems="center" sx={{ py: 2 }}>
          <Grid item xs={12} sm={7} md={8} lg={9}>
            <Typography variant="h5" sx={{ mb: 1, ml: 3 }}>
              {title || t('personalInformation')}
            </Typography>
            {subtitle && <Typography sx={{ ml: 3 }}>{subtitle}</Typography>}
          </Grid>
          {isClientSummary && (
            <Grid item xs={6} sm={5} md={4} lg={3} sx={{ pl: { md: 5, sm: 3 } }}>
              <Grid item xs={12}>
                <Typography color="primary">
                  <strong>
                    {t('individual')}:{' '}
                    {`${demographic.individuals[0].firstName} ${demographic.individuals[0].lastName}`}
                  </strong>
                </Typography>
              </Grid>
            </Grid>
          )}
        </Grid>

        {/* Individual Details */}
        {demographicFields?.individuals?.map((individual, index) => (
          <Grid key={index}>
            {isMultipleIndividualEnabled && (
              <Individual
                index={index}
                demographic={demographic}
                demographicFields={demographicFields}
                individual={individual}
                handleRemoveIndividual={handleRemoveIndividual}
                handleIndividualChangeCallback={handleIndividualChangeCallback}
                handlePreferredContactMethodCallback={handlePreferredContactMethod}
                fields={fields}
                isClientSummary={isClientSummary}
                validationData={showValidationErrors ? validationData : {}}
                setTriggerUseEffect={setTriggerUseEffect}
                userInfoSuccessData={getIndividualDataSuccessData}
                widgetType={widgetType}
                setShowValidationErrors={setShowValidationErrors}
                otpVerificationEnabled={otpVerificationEnabled}
              />
            )}
            {!isMultipleIndividualEnabled && (
              <AllDemographic
                index={index}
                demographic={demographic}
                demographicFields={demographicFields}
                individual={individual}
                handleRemoveIndividual={handleRemoveIndividual}
                handleIndividualChangeCallback={handleIndividualChangeCallback}
                provinces={provinces}
                preferredContactMethod={demographicFields.preferredContactMethod}
                handlePreferredContactMethodCallback={handlePreferredContactMethod}
                contact={demographicFields.contact}
                handleContactChangeCallback={handleContactChangeCallback}
                fields={fields}
                isClientSummary={isClientSummary}
                validationData={showValidationErrors ? validationData : {}}
                setTriggerUseEffect={setTriggerUseEffect}
                userInfoSuccessData={getIndividualDataSuccessData}
                widgetType={widgetType}
                setShowValidationErrors={setShowValidationErrors}
                otpVerificationEnabled={otpVerificationEnabled}
                primaryEmailVerified={primaryEmailVerified}
                primaryPhoneVerified={primaryPhoneVerified}
                onVerificationSuccess={handleVerificationSuccess}
                verifiedValues={verifiedValues}
              />
            )}
          </Grid>
        ))}

        {isMultipleIndividualEnabled && (
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' }, mb: 4, ml: 3, mt: 2 }}
            onClick={handleAddMultipleIndividual}
          >
            {t('addIndividual')}
          </Button>
        )}

        {/* Contact Details */}
        {isMultipleIndividualEnabled && (
          <Grid sx={{ pb: 2, mb: 1 }}>
            <Contact
              demographic={demographic}
              demographicFields={demographicFields}
              preferredContactMethod={demographicFields.preferredContactMethod}
              handlePreferredContactMethodCallback={handlePreferredContactMethod}
              contact={demographicFields.contact}
              handleContactChangeCallback={handleContactChangeCallback}
              fields={fields}
              isClientSummary={isClientSummary}
              validationData={showValidationErrors ? validationData : {}}
              setTriggerUseEffect={setTriggerUseEffect}
              userInfoSuccessData={getIndividualDataSuccessData}
              widgetType={widgetType}
              setShowValidationErrors={setShowValidationErrors}
              otpVerificationEnabled={otpVerificationEnabled}
              primaryEmailVerified={primaryEmailVerified}
              primaryPhoneVerified={primaryPhoneVerified}
              onVerificationSuccess={handleVerificationSuccess}
              verifiedValues={verifiedValues}
            />
          </Grid>
        )}
        {isConsentRequired !== false && widgetType !== 'REGISTRATION' && (
          <Box sx={{ px: 2, py: 1, m: 1 }}>
            <ConsentAgreement isConsented={isConsented} handleConsentChange={() => setIsConsented(!isConsented)} />
          </Box>
        )}
        <Grid container justifyContent="left" sx={{ pt: 2, px: 2, pb: 2 }}>
          <Grid item xs={12} sm={10} md={6} lg={5} container justifyContent="center">
            {isClientSummary && (
              <Button
                variant="outlined"
                onClick={() => handleSummaryNavigationCallback(actions.PREVIOUS)}
                sx={{ mr: 2 }}
              >
                {t('back')}
              </Button>
            )}
            <Button
              variant="contained"
              onClick={handleOnClick}
              disabled={
                (widgetType === 'REGISTRATION' ? false : isConsentRequired !== false && !isConsented) ||
                needsVerification() ||
                hasValidationErrors() ||
                fields.length <= 0
              }
            >
              {t('next')}
            </Button>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};
